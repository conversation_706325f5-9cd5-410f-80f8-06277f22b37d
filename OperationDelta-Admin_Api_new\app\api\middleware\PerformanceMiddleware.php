<?php
declare(strict_types=1);

namespace app\api\middleware;

use app\api\service\PerformanceMonitor;
use think\facade\Log;
use think\facade\Config;

/**
 * API性能监控中间件
 * 自动为所有API控制器添加性能监控
 */
class PerformanceMiddleware
{
    private static array $monitors = [];
    
    public function handle($request, \Closure $next)
    {
        // 检查是否启用性能监控
        if (!$this->isEnabled()) {
            return $next($request);
        }
        
        // 获取请求信息
        $controller = $this->getControllerName($request);
        $action = $this->getActionName($request);
        $apiName = $controller . '::' . $action;
        
        // 检查是否需要跳过监控
        if ($this->shouldSkipMonitoring($controller, $action)) {
            return $next($request);
        }
        
        // 创建性能监控器
        $monitor = new PerformanceMonitor();
        self::$monitors[$apiName] = $monitor;
        
        // 开始监控
        $this->startMonitoring($monitor, $request, $apiName);
        
        try {
            // 执行请求
            $monitor->startTimer('request_processing');
            $response = $next($request);
            $processingTime = $monitor->endTimer('request_processing');
            
            // 分析响应
            $this->analyzeResponse($monitor, $response, $processingTime);
            
            $monitor->endTimer('middleware_total');
            
            // 生成性能报告
            $this->generateAndLogReport($monitor, $request, $apiName);
            
            return $response;
            
        } catch (\Throwable $e) {
            $this->handleException($monitor, $e, $request, $apiName);
            throw $e;
        } finally {
            // 清理监控器
            unset(self::$monitors[$apiName]);
        }
    }
    
    /**
     * 检查是否启用性能监控
     */
    private function isEnabled(): bool
    {
        return Config::get('performance.enabled', true);
    }
    
    /**
     * 获取控制器名称
     */
    private function getControllerName($request): string
    {
        $controller = $request->controller();
        // 移除命名空间，只保留类名
        if (strpos($controller, '\\') !== false) {
            $controller = substr(strrchr($controller, '\\'), 1);
        }
        return $controller;
    }
    
    /**
     * 获取方法名称
     */
    private function getActionName($request): string
    {
        return $request->action();
    }
    
    /**
     * 检查是否需要跳过监控
     */
    private function shouldSkipMonitoring(string $controller, string $action): bool
    {
        // 跳过系统内置控制器
        $skipControllers = ['Error', 'Miss', 'Index'];
        if (in_array($controller, $skipControllers)) {
            return true;
        }
        
        // 检查是否在监控白名单中
        $monitoredApis = Config::get('performance.monitored_apis', []);
        if (!empty($monitoredApis)) {
            $apiIdentifier = $controller . '::' . $action;
            return !in_array($apiIdentifier, $monitoredApis) && !in_array($action, $monitoredApis);
        }
        
        // 检查是否在黑名单中
        $skipActions = Config::get('performance.skip_actions', ['ping', 'health', 'status']);
        return in_array($action, $skipActions);
    }
    
    /**
     * 开始监控
     */
    private function startMonitoring(PerformanceMonitor $monitor, $request, string $apiName): void
    {
        $monitor->startTimer('middleware_total');
        $monitor->recordMetric('request_method', $request->method());
        $monitor->recordMetric('request_url', $request->url(true));
        $monitor->recordMetric('request_ip', $request->ip());
        $monitor->recordMetric('user_agent', $request->header('User-Agent', 'Unknown'));
        
        // 记录请求参数数量
        $params = $request->param();
        $monitor->recordMetric('request_params_count', count($params), 'count');
        
        // 记录请求大小（粗略估算）
        $contentLength = $request->header('Content-Length', 0);
        if ($contentLength > 0) {
            $monitor->recordMetric('request_size', $contentLength, 'bytes');
        }
    }
    
    /**
     * 分析响应
     */
    private function analyzeResponse(PerformanceMonitor $monitor, $response, float $processingTime): void
    {
        // 记录响应状态码
        if (method_exists($response, 'getCode')) {
            $monitor->recordMetric('http_status_code', $response->getCode());  
        }
        
        // 分析响应数据
        $responseData = null;
        if (method_exists($response, 'getData')) {
            $responseData = $response->getData();
        } elseif (method_exists($response, 'getContent')) {
            $content = $response->getContent();
            $responseData = json_decode($content, true);
        }
        
        if (is_array($responseData)) {
            // 记录API返回码
            if (isset($responseData['code'])) {
                $monitor->recordMetric('api_result_code', $responseData['code']);
                $monitor->recordMetric('api_success', $responseData['code'] == 1 ? 'true' : 'false');
            }
            
            // 记录返回数据量
            if (isset($responseData['data']) && is_array($responseData['data'])) {
                if (isset($responseData['data']['list'])) {
                    $listCount = count($responseData['data']['list']);
                    $monitor->recordMetric('response_records', $listCount, 'records');
                    
                    // 计算平均处理时间每条记录
                    if ($listCount > 0 && $processingTime > 0) {
                        $avgTimePerRecord = $processingTime / $listCount;
                        $monitor->recordMetric('avg_time_per_record', round($avgTimePerRecord, 6), 'seconds');
                    }
                }
                
                if (isset($responseData['data']['total'])) {
                    $monitor->recordMetric('total_available_records', $responseData['data']['total'], 'records');
                }
                
                if (isset($responseData['data']['page']) && isset($responseData['data']['page_size'])) {
                    $monitor->recordMetric('page_info', $responseData['data']['page'] . '/' . $responseData['data']['page_size']);
                }
            }
            
            // 估算响应大小
            $responseSize = strlen(json_encode($responseData));
            $monitor->recordMetric('response_size', $responseSize, 'bytes');
        }
    }
    
    /**
     * 生成并记录性能报告
     */
    private function generateAndLogReport(PerformanceMonitor $monitor, $request, string $apiName): void
    {
        // 收集请求参数（敏感信息过滤）
        $params = $this->filterSensitiveParams($request->param());
        $params['method'] = $request->method();
        $params['url'] = $request->url(true);
        
        // 生成性能报告
        $report = $monitor->generateReport($apiName, $params);
        
        // 检查性能告警
        $this->checkPerformanceAlerts($report, $apiName);
        
        // 记录额外的统计信息
        $this->logAdditionalStats($report, $apiName);
    }
    
    /**
     * 处理异常情况
     */
    private function handleException(PerformanceMonitor $monitor, \Throwable $e, $request, string $apiName): void
    {
        if (isset($monitor)) {
            $monitor->endTimer('request_processing');
            $monitor->endTimer('middleware_total');
            
            // 记录错误信息
            $monitor->recordMetric('error_occurred', true);
            $monitor->recordMetric('error_type', get_class($e));
            $monitor->recordMetric('error_code', $e->getCode());
            
            $params = [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'method' => $request->method(),
                'url' => $request->url(true)
            ];
            
            $monitor->generateReport($apiName . '_error', $params);
        }
    }
    
    /**
     * 过滤敏感参数
     */
    private function filterSensitiveParams(array $params): array
    {
        $sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth', 'authorization'];
        
        foreach ($params as $key => $value) {
            $lowerKey = strtolower($key);
            foreach ($sensitiveKeys as $sensitiveKey) {
                if (strpos($lowerKey, $sensitiveKey) !== false) {
                    $params[$key] = '***';
                    break;
                }
            }
        }
        
        return $params;
    }
    
    /**
     * 记录额外的统计信息
     */
    private function logAdditionalStats(array $report, string $apiName): void
    {
        // 如果启用详细统计，记录更多信息
        if (Config::get('performance.detailed_stats', false)) {
            Log::info('[性能统计] API调用统计', [
                'api' => $apiName,
                'request_id' => $report['request_id'],
                'duration' => $report['total_duration'],
                'memory' => $report['memory_used'],
                'level' => $report['performance_level'],
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    /**
     * 检查性能告警
     */
    private function checkPerformanceAlerts(array $report, string $apiName): void
    {
        if (!Config::get('performance.enable_alerts', true)) {
            return;
        }
        
        $alerts = [];
        
        // 检查总耗时
        $totalThreshold = Config::get('performance.slow_query_thresholds.total_request', 2.0);
        if ($report['total_duration'] > $totalThreshold) {
            $alerts[] = "API响应时间过长: {$report['total_duration']}s (阈值: {$totalThreshold}s)";
        }
        
        // 检查内存使用
        $memoryBytes = $this->parseMemoryToBytes($report['memory_used']);
        $memoryThreshold = Config::get('performance.memory_thresholds.warning', 50 * 1024 * 1024);
        if ($memoryBytes > $memoryThreshold) {
            $alerts[] = "内存使用过高: {$report['memory_used']} (阈值: " . $this->formatMemory($memoryThreshold) . ")";
        }
        
        // 检查缓存命中率
        if (isset($report['cache_hit_rate'])) {
            $cacheThreshold = Config::get('performance.alert_config.low_cache_hit_rate_threshold', 70);
            if ($report['cache_hit_rate'] < $cacheThreshold) {
                $alerts[] = "缓存命中率过低: {$report['cache_hit_rate']}% (阈值: {$cacheThreshold}%)";
            }
        }
        
        // 检查数据库查询次数
        if (isset($report['counters']['db_queries'])) {
            $dbQueryThreshold = Config::get('performance.alert_config.max_db_queries', 10);
            if ($report['counters']['db_queries'] > $dbQueryThreshold) {
                $alerts[] = "数据库查询次数过多: {$report['counters']['db_queries']}次 (阈值: {$dbQueryThreshold}次)";
            }
        }
        
        // 发送告警
        if (!empty($alerts)) {
            Log::warning('[性能告警] API性能异常', [
                'api' => $apiName,
                'request_id' => $report['request_id'],
                'alerts' => $alerts,
                'performance_level' => $report['performance_level'],
                'duration' => $report['total_duration'],
                'memory' => $report['memory_used'],
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    /**
     * 解析内存字符串为字节数
     */
    private function parseMemoryToBytes(string $memory): int
    {
        $units = ['B' => 1, 'KB' => 1024, 'MB' => 1024*1024, 'GB' => 1024*1024*1024];
        
        foreach ($units as $unit => $multiplier) {
            if (str_ends_with($memory, $unit)) {
                return (int)(floatval(str_replace($unit, '', $memory)) * $multiplier);
            }
        }
        
        return 0;
    }
    
    /**
     * 格式化内存大小
     */
    private function formatMemory(int $bytes): string
    {
        if ($bytes >= 1024 * 1024 * 1024) {
            return round($bytes / (1024 * 1024 * 1024), 2) . 'GB';
        } elseif ($bytes >= 1024 * 1024) {
            return round($bytes / (1024 * 1024), 2) . 'MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . 'KB';
        } else {
            return $bytes . 'B';
        }
    }
    
    /**
     * 获取当前监控器实例
     */
    public static function getCurrentMonitor(string $apiName = ''): ?PerformanceMonitor
    {
        if (empty($apiName)) {
            return !empty(self::$monitors) ? current(self::$monitors) : null;
        }
        
        return self::$monitors[$apiName] ?? null;
    }
}