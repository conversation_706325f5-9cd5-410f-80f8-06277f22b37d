<?php
declare(strict_types=1);

namespace app\api\controller;

use think\facade\Db;
use think\facade\Cache;
use think\Request;
use think\facade\Log;
use app\api\controller\BaseApiController;
use app\api\service\CacheManager;
use app\api\service\RankingService;
use app\api\service\PerformanceMonitor;
use think\App;

/**
 * 价格排行榜相关API控制器
 * 
 * @api {get} /api/ranking/list 获取价格排行榜数据
 * @apiVersion 1.0.0
 * @apiName getRankingList
 * @apiGroup Ranking
 * 
 * @apiParam {String} [type=highest_price] 排序类型（highest_price/lowest_price/increase_percentage）
 * @apiParam {String} [time_range=day] 时间范围（hour/day/week/month）
 * @apiParam {Number} [page=1] 页码
 * @apiParam {Number} [page_size=20] 每页数量
 * @apiParam {Number} [grade] 物品等级筛选
 * @apiParam {String} [item_type] 物品类型筛选
 * @apiParam {Number} [min_price] 最小价格筛选
 * @apiParam {Number} [max_price] 最大价格筛选
 * 
 * @apiSuccess {Number} code 状态码（1成功，0失败）
 * @apiSuccess {String} msg 提示信息
 * @apiSuccess {Number} time 时间戳
 * @apiSuccess {Object} data 返回数据
 * @apiSuccess {Array} data.list 列表数据
 * @apiSuccess {Number} data.total 总记录数
 * @apiSuccess {Number} data.page 当前页码
 * @apiSuccess {Number} data.page_size 每页数量
 * 
 * 重构记录:
 * 2025-07-27: 智能缓存策略重构
 *   - 引入智能缓存管理系统，支持价格数据整点2分钟更新机制
 *   - 排行榜数据使用价格缓存策略，保证数据时效性
 *   - 保持100%功能兼容性，响应格式完全不变
 */
class Ranking extends BaseApiController
{
    // 新增钥匙卡排行榜接口无需登录
    protected array $noNeedLogin = ['getRankingList', 'getKeycardRanking','getBulletRanking','getBulletPackageRanking','getBulletPrices'];
    protected array $noNeedPermission = [];
    private string $currentType = 'highest_price';
    
    
    private CacheManager $cacheManager;
    private RankingService $rankingService;
    private PerformanceMonitor $performanceMonitor;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->cacheManager = new CacheManager();
        $this->rankingService = new RankingService();
        $this->performanceMonitor = new PerformanceMonitor();
    }

    /**
     * 获取价格排行榜数据
     *
     * @param Request $request
     * @return \think\Response
     */
    public function getRankingList(Request $request)
    {
        // 开始性能监控
        $this->performanceMonitor->startTimer('total_request');
        
        try {
            // 参数验证阶段
            $this->performanceMonitor->startTimer('param_validation');
            $params = $this->validateAndGetParams($request);
            $this->currentType = $params['type'];
            $this->performanceMonitor->endTimer('param_validation');
            
            // 记录请求参数
            $this->performanceMonitor->recordMetric('request_params', count($params), 'count');
            Log::info('开始处理价格排行榜数据请求，参数：' . json_encode($params, JSON_UNESCAPED_UNICODE));
            
            // 缓存查询阶段
            $this->performanceMonitor->startTimer('cache_lookup');
            $cacheKey = 'ranking_list:' . md5(json_encode($params));
            
            $cacheStart = microtime(true);
            $result = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($params) {
                return $this->fetchRankingListData($params);
            });
            $cacheDuration = microtime(true) - $cacheStart;
            
            // 判断是否命中缓存
            $cacheHit = $cacheDuration < 0.01; // 如果耗时很短，可能是缓存命中
            $this->performanceMonitor->recordCacheOperation('get', $cacheKey, $cacheHit, $cacheDuration);
            $this->performanceMonitor->endTimer('cache_lookup');
            
            // 记录结果指标
            if (isset($result['list'])) {
                $this->performanceMonitor->recordMetric('result_count', count($result['list']), 'records');
                $this->performanceMonitor->recordMetric('total_records', $result['total'] ?? 0, 'records');
            }
            
            $this->performanceMonitor->endTimer('total_request');
            
            // 生成性能报告
            $perfReport = $this->performanceMonitor->generateReport('getRankingList', $params);
            
            // 使用基础类提供的成功响应方法
            return $this->success('获取排行榜数据成功', $result);

        } catch (\Exception $e) {
            $this->performanceMonitor->endTimer('total_request');
            $this->performanceMonitor->generateReport('getRankingList_error', ['error' => $e->getMessage()]);
            
            Log::error('getRankingList error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->error('获取排行榜数据失败：' . $e->getMessage());
        }
    }

    /**
     * 获取排行榜数据的核心逻辑
     */
    private function fetchRankingListData(array $params): array
    {
        $startTime = microtime(true);
        
        // 获取列表数据
        $this->performanceMonitor->startTimer('get_list_data');
        try {
            $list = $this->getListData($params);
            $getListTime = $this->performanceMonitor->endTimer('get_list_data');
            $this->performanceMonitor->recordMetric('list_query_records', count($list), 'records');
            $this->performanceMonitor->checkSlowQuery('get_list_data', $getListTime, 0.5);
        } catch (\Throwable $listEx) {
            $this->performanceMonitor->endTimer('get_list_data');
            Log::error('获取列表数据方法失败: ' . $listEx->getMessage());
            throw $listEx;
        }
        
        // 获取总记录数
        $this->performanceMonitor->startTimer('get_total_count');
        try {
            $total = $this->getTotal($params);
            $getTotalTime = $this->performanceMonitor->endTimer('get_total_count');
            $this->performanceMonitor->recordMetric('total_count', $total, 'records');
            $this->performanceMonitor->checkSlowQuery('get_total_count', $getTotalTime, 0.3);
        } catch (\Throwable $totalEx) {
            $this->performanceMonitor->endTimer('get_total_count');
            Log::error('获取总记录数方法失败: ' . $totalEx->getMessage());
            $total = count($list);
            $this->performanceMonitor->recordMetric('total_count_fallback', $total, 'records');
        }
        
        // 价格变化数据处理
        $this->performanceMonitor->startTimer('process_price_changes');
        try {
            $list = $this->optimizedProcessPriceChanges($list, $params['time_range']);
            $processTime = $this->performanceMonitor->endTimer('process_price_changes');
            $this->performanceMonitor->checkSlowQuery('process_price_changes', $processTime, 0.2);
        } catch (\Throwable $processEx) {
            $this->performanceMonitor->endTimer('process_price_changes');
            Log::error('价格变化数据处理失败: ' . $processEx->getMessage());
        }
        
        // 检查内存使用
        $this->performanceMonitor->checkMemoryUsage('after_data_processing');
        
        // 计算总耗时
        $queryTime = round(microtime(true) - $startTime, 4);
        $this->performanceMonitor->recordMetric('data_fetch_duration', $queryTime, 'seconds');
        
        // 构建返回数据
        return [
            'list' => $list,
            'total' => $total,
            'page' => $params['page'],
            'page_size' => $params['page_size'],
            'query_time' => $queryTime
        ];
    }

    /**
     * 获取列表数据
     *
     * @param array $params
     * @return array
     */
    private function getListData(array $params): array
    {
        try {
            $startTime = microtime(true);
            
            // 构建缓存键
            $cacheKey = 'ranking:list_data:' . md5(json_encode($params));
            
            // 构建查询对象
            $queryBuildStartTime = microtime(true);
            $query = $this->buildBaseQuery($params);
            $queryBuildTime = round(microtime(true) - $queryBuildStartTime, 4);
            
            // 先查询总记录数，以便进行准确的分页
            // 使用getTotal方法，它现在会使用相同的查询条件
            $total = $this->getTotal($params);
            Log::info('列表分页计数结果: total=' . $total);
            
            // 修正页码，确保不超出实际范围
            $page = max(1, intval($params['page']));
            $pageSize = min(intval($params['page_size']), 100); // 最多返回100条记录
            $maxPage = max(1, ceil($total / $pageSize));
            
            if ($page > $maxPage) {
                $page = $maxPage;
                Log::info('页码超出范围，已自动调整为最大页码: ' . $maxPage);
            }
            
            // 计算正确的偏移量
            $offset = ($page - 1) * $pageSize;
            
            // 记录分页信息
            Log::info('分页信息: 总记录数=' . $total . ', 页码=' . $page . '/' . $maxPage . ', 每页=' . $pageSize . ', 偏移量=' . $offset);
            
            // 关闭查询日志和分析，提高性能
            $query->fetchSql(false);
            
            // 执行查询
            $queryExecStartTime = microtime(true);
            
            // 记录实际执行的SQL，用于调试
            try {
                $debugSql = (clone $query)->limit($offset, $pageSize)->fetchSql(true)->select();
                // 限制SQL长度
                if (strlen($debugSql) > 500) {
                    $debugSql = substr($debugSql, 0, 497) . '...';
                }
                Log::info('执行分页查询SQL: ' . $debugSql);
            } catch (\Throwable $sqlEx) {
                Log::warning('无法获取调试SQL: ' . $sqlEx->getMessage());
            }
            
            // 真正执行查询
            $list = $query->limit($offset, $pageSize)->select()->toArray();
            $queryExecTime = round(microtime(true) - $queryExecStartTime, 4);
            
            // 记录数据库查询性能
            $this->performanceMonitor->recordDbQuery('list_data_query', $queryExecTime, count($list));
            
            // 使用CacheManager缓存结果数据
            $cacheStart = microtime(true);
            $cachedList = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($list) {
                return $list;
            });
            $cacheDuration = round(microtime(true) - $cacheStart, 4);
            
            // 记录缓存操作
            $cacheHit = $cacheDuration < 0.001; // 极短时间可能是缓存命中
            $this->performanceMonitor->recordCacheOperation('set', $cacheKey, $cacheHit, $cacheDuration);
            
            $totalTime = round(microtime(true) - $startTime, 4);
            Log::info('性能日志：获取列表数据完成，总耗时：' . $totalTime . '秒，查询构建耗时：' . $queryBuildTime . '秒，查询执行耗时：' . 
                $queryExecTime . '秒，记录数：' . count($list));
            
            return $list;
        } catch (\Throwable $e) {
            // 记录详细错误并重新抛出
            Log::error('获取列表数据失败: ' . $e->getMessage() . ' in ' . $e->getFile() . ' line ' . $e->getLine());
            Log::error('获取列表数据堆栈: ' . $e->getTraceAsString());
            Log::error('参数信息: ' . json_encode($params, JSON_UNESCAPED_UNICODE));
            
            // 抛出异常给上层处理
            throw $e;
        }
    }

    /**
     * 获取总记录数 - 优化版本
     *
     * @param array $params
     * @return int
     */
    private function getTotal(array $params): int
    {
        try {
            $startTime = microtime(true);
            
            // 构建缓存键
            $cacheKey = 'ranking:total:' . md5(json_encode($params));
            
            // 使用CacheManager尝试从缓存获取数据
            $cachedTotal = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() {
                return null;
            });
            if ($cachedTotal !== null) {
                Log::info('从缓存获取总记录数: ' . $cachedTotal);
                return (int)$cachedTotal;
            }
            
            // 获取时间范围
            $timeRange = $params['time_range'] ?? 'day';
            $startDate = match($timeRange) {
                'hour' => date('Y-m-d H:00:00', strtotime('-1 hour')),
                'day' => date('Y-m-d 00:00:00'),
                'week' => date('Y-m-d H:i:s', strtotime('-7 day')),
                'month' => date('Y-m-d H:i:s', strtotime('-30 day')),
                default => date('Y-m-d 00:00:00')
            };
            
            // 优化计数查询：简化查询结构
            $query = Db::name('sjz_items')
                ->alias('i')
                ->where('i.delete_time', null)
                ->join("(SELECT object_id, MAX(price) as current_price 
                        FROM ba_sjz_price_history 
                        WHERE timestamp >= '{$startDate}' AND price > 0 
                        GROUP BY object_id) p", 'i.object_id = p.object_id', 'INNER');
            
            // 应用筛选条件
            if (!empty($params['grade']) && is_numeric($params['grade'])) {
                $query->where('i.grade', intval($params['grade']));
            }
            
            if (!empty($params['item_type'])) {
                $query->join('ba_sjz_item_categories c', 'i.category_id = c.id', 'INNER')
                    ->where(function($q) use ($params) {
                        $q->where('c.primary_class', $params['item_type'])
                          ->whereOr('c.second_class', $params['item_type']);
                    });
            }
            
            if (isset($params['min_price']) && is_numeric($params['min_price']) && $params['min_price'] > 0) {
                $query->where('p.current_price', '>=', floatval($params['min_price']));
            }
            
            if (isset($params['max_price']) && is_numeric($params['max_price']) && $params['max_price'] > 0) {
                $query->where('p.current_price', '<=', floatval($params['max_price']));
            }
            
            // 简化计数查询
            $queryStart = microtime(true);
            $total = (int)$query->count('i.object_id');
            $queryDuration = round(microtime(true) - $queryStart, 4);
            
            // 记录数据库查询性能
            $this->performanceMonitor->recordDbQuery('count_query', $queryDuration, $total);
            
            // 缓存结果
            $this->cacheManager->remember($cacheKey . '_result', CacheManager::TYPE_PRICE_DATA, function() use ($total) {
                return $total;
            });
            
            $executeTime = round(microtime(true) - $startTime, 4);
            Log::info('优化计数查询完成，耗时：' . $executeTime . '秒，总数：' . $total);
            
            return (int)$total;
        } catch (\Throwable $e) {
            Log::error('计数查询失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 构建基础查询
     *
     * @param array $params
     * @return \think\db\Query
     */
    private function buildBaseQuery(array $params): \think\db\Query
    {
        try {
            $startTime = microtime(true);
            
            // 不直接缓存Query对象，因为它们包含资源句柄，不能被序列化
            // 但可以缓存构建过程中的复杂条件或中间结果
            
            // 直接构建基础查询
            $query = Db::name('sjz_items')
                ->alias('i')
                ->join('ba_sjz_item_categories c', 'i.category_id = c.id', 'left')
                ->where('i.delete_time', null);
            
            $buildTime = round(microtime(true) - $startTime, 4);
            Log::info('性能日志：初始查询构建完成，耗时：' . $buildTime . '秒');
                
            // 不再检查is_active字段，直接应用条件
            $filterStartTime = microtime(true);
            $this->applyFilters($query, $params);
            $filterTime = round(microtime(true) - $filterStartTime, 4);
            
            $timeRangeStartTime = microtime(true);
            $this->applyTimeRangeConditions($query, $params['time_range']);
            $timeRangeTime = round(microtime(true) - $timeRangeStartTime, 4);
            
            $fieldStartTime = microtime(true);
            $this->applyFieldSelection($query, $params['time_range']);
            $fieldTime = round(microtime(true) - $fieldStartTime, 4);
            
            $sortStartTime = microtime(true);
            $this->applySorting($query, $this->getCurrentType());
            $sortTime = round(microtime(true) - $sortStartTime, 4);
            
            $totalTime = round(microtime(true) - $startTime, 4);
            Log::info('性能日志：构建查询对象完成，总耗时：' . $totalTime . '秒，过滤耗时：' . $filterTime . '秒，时间范围耗时：' . 
                $timeRangeTime . '秒，字段选择耗时：' . $fieldTime . '秒，排序耗时：' . $sortTime . '秒');
            
            return $query;
        } catch (\Throwable $e) {
            Log::error('构建查询对象失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回最简单的查询，保证程序不中断
            return Db::name('sjz_items')->alias('i');
        }
    }

    /**
     * 优化的价格变化数据处理
     *
     * @param array $list
     * @param string $timeRange
     * @return array
     */
    private function optimizedProcessPriceChanges(array $list, string $timeRange): array
    {
        $startTime = microtime(true);
        
        // 批量处理，减少单个操作
        foreach ($list as &$item) {
            // 确保价格数据类型转换
            $currentPrice = (float)($item['current_price'] ?? 0);
            $previousPrice = (float)($item['previous_price'] ?? 0);
            
            // 计算单位价格
            $width = (float)($item['width'] ?? 1);
            $length = (float)($item['length'] ?? 1);
            $area = ($width > 0 && $length > 0) ? ($width * $length) : 1;
            $item['unit_price'] = round($currentPrice / $area, 2);
            
            // 简化价格变化计算
            if ($previousPrice > 0) {
                $priceChange = $currentPrice - $previousPrice;
                $priceChangePercentage = ($priceChange / $previousPrice) * 100;
                
                $item['price_change'] = round($priceChange, 2);
                $item['price_change_percentage'] = round($priceChangePercentage, 2);
                $item['trend'] = $priceChangePercentage > 0.1 ? 'up' : ($priceChangePercentage < -0.1 ? 'down' : 'stable');
            } else {
                $item['price_change'] = 0;
                $item['price_change_percentage'] = 0;
                $item['trend'] = 'stable';
            }
            
            // 确保数据类型
            $item['current_price'] = $currentPrice;
            $item['previous_price'] = $previousPrice;
        }
        
        $executeTime = round(microtime(true) - $startTime, 4);
        Log::info('优化价格变化处理完成，耗时：' . $executeTime . '秒，记录：' . count($list));
        
        return $list;
    }

    /**
     * 处理价格变化数据
     *
     * @param array $list
     * @param string $timeRange
     * @return array
     */
    private function processPriceChanges(array $list, string $timeRange): array
    {
        $startTime = microtime(true);
        
        // 构建缓存键，基于列表数据和时间范围
        $dataHash = md5(json_encode($list) . $timeRange);
        $cacheKey = 'ranking:price_changes:' . $dataHash;
        
        // 使用CacheManager尝试从缓存获取数据
        $cachedData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() {
            return null; // 如果缓存不存在，返回null继续执行处理
        });
        if ($cachedData !== null) {
            Log::info('从缓存获取价格变化数据处理结果');
            return $cachedData;
        }
        
        // 用于记录处理情况的计数器
        $stats = [
            'total' => count($list),
            'with_previous_price' => 0,
            'without_previous_price' => 0,
            'up_trend' => 0,
            'down_trend' => 0,
            'stable_trend' => 0
        ];
        
        // 批量处理所有记录
        foreach ($list as &$item) {
            // 转换价格数据为浮点数
            $currentPrice = isset($item['current_price']) ? (float)$item['current_price'] : 0;
            $previousPrice = isset($item['previous_price']) ? (float)$item['previous_price'] : 0;
            
            // 确保所有价格数据都是浮点数
            if (isset($item['lowest_price'])) $item['lowest_price'] = (float)$item['lowest_price'];
            if (isset($item['highest_price'])) $item['highest_price'] = (float)$item['highest_price'];
            if (isset($item['average_price'])) $item['average_price'] = (float)$item['average_price'];
            
            // 计算单格价格(单位价格)
            $width = isset($item['width']) ? (float)$item['width'] : 1;
            $length = isset($item['length']) ? (float)$item['length'] : 1;
            
            // 避免除以零的情况
            $area = ($width > 0 && $length > 0) ? ($width * $length) : 1;
            $unitPrice = $currentPrice / $area;
            
            // 加入单位价格字段
            $item['unit_price'] = round($unitPrice, 2);
            
            // 处理价格变化
            if ($previousPrice <= 0) {
                $stats['without_previous_price']++;
                $priceChange = 0;
                $priceChangePercentage = 0;
                $trend = 'stable';
            } else {
                $stats['with_previous_price']++;
                // 计算价格变化
                $priceChange = $currentPrice - $previousPrice;
                
                // 计算变化百分比
                $priceChangePercentage = ($priceChange / $previousPrice) * 100;
                
                // 确定价格趋势，添加0.1%的阈值
                if ($priceChangePercentage > 0.1) {
                    $trend = 'up';
                    $stats['up_trend']++;
                } elseif ($priceChangePercentage < -0.1) {
                    $trend = 'down';
                    $stats['down_trend']++;
                } else {
                    $trend = 'stable';
                    $stats['stable_trend']++;
                }
            }
            
            // 四舍五入保留两位小数
            $priceChange = round($priceChange, 2);
            $priceChangePercentage = round($priceChangePercentage, 2);
            
            // 更新价格字段
            $item['current_price'] = $currentPrice;
            $item['previous_price'] = $previousPrice;
            $item['price_change'] = $priceChange;
            $item['price_change_percentage'] = $priceChangePercentage;
            $item['trend'] = $trend;
            
            // 跳过小时查询的额外计算
            if ($timeRange === 'hour') {
                continue;
            }
            
            // 计算价格对比数据
            $this->calculatePriceComparisons($item);
        }
        
        // 使用CacheManager缓存处理结果
        $this->cacheManager->remember($cacheKey . '_processed', CacheManager::TYPE_PRICE_DATA, function() use ($list) {
            return $list;
        });
        
        $executeTime = round(microtime(true) - $startTime, 4);
        $avgPerItem = $executeTime > 0 && count($list) > 0 ? round($executeTime / count($list), 6) : 0;
        
        Log::info('优化的价格变化处理完成，耗时：' . $executeTime . '秒，记录：' . count($list) . 
            '，平均每条：' . $avgPerItem . '秒，统计：' . json_encode($stats));
        
        return $list;
    }

    /**
     * 验证并获取请求参数
     *
     * @param Request $request
     * @return array
     */
    private function validateAndGetParams(Request $request): array
    {
        $params = $request->param();
        $type = $params['type'] ?? 'highest_price';
        
        // 记录接收到的排序类型参数
        Log::info('接收到的排序类型参数: ' . $type);
        
        return [
            'type' => $type,
            'time_range' => $params['time_range'] ?? 'day',
            'page' => (int)($params['page'] ?? 1),
            'page_size' => (int)($params['page_size'] ?? 20),
            'grade' => (int)($params['grade'] ?? 0),
            'item_type' => $params['item_type'] ?? '',
            'min_price' => isset($params['min_price']) ? (float)$params['min_price'] : null,
            'max_price' => isset($params['max_price']) ? (float)$params['max_price'] : null,
        ];
    }

    /**
     * 应用筛选条件
     *
     * @param \think\db\Query $query
     * @param array $params
     * @return void
     */
    private function applyFilters(\think\db\Query $query, array $params): void
    {
        // 应用等级筛选
        if (!empty($params['grade']) && is_numeric($params['grade'])) {
            $query->where('i.grade', intval($params['grade']));
            Log::info('应用等级过滤条件: grade=' . intval($params['grade']));
        }

        // 应用物品类型筛选
        if (!empty($params['item_type'])) {
            $query->where(function($q) use ($params) {
                $q->where('c.primary_class', $params['item_type'])
                  ->whereOr('c.second_class', $params['item_type']);
            });
            Log::info('应用物品类型过滤条件: item_type=' . $params['item_type']);
        }

        // 应用价格范围筛选
        if (isset($params['min_price']) && is_numeric($params['min_price']) && $params['min_price'] > 0) {
            $query->where('p.current_price', '>=', floatval($params['min_price']));
            Log::info('应用最小价格过滤条件: min_price=' . floatval($params['min_price']));
        }

        if (isset($params['max_price']) && is_numeric($params['max_price']) && $params['max_price'] > 0) {
            $query->where('p.current_price', '<=', floatval($params['max_price']));
            Log::info('应用最大价格过滤条件: max_price=' . floatval($params['max_price']));
        }
    }

    /**
     * 应用时间范围条件 - 优化版本
     *
     * @param \think\db\Query $query
     * @param string $timeRange
     * @return void
     */
    private function applyTimeRangeConditions(\think\db\Query $query, string $timeRange): void
    {
        // 根据时间范围确定起始日期
        $startDate = match($timeRange) {
            'hour' => date('Y-m-d H:00:00', strtotime('-1 hour')),
            'day' => date('Y-m-d 00:00:00'),
            'week' => date('Y-m-d H:i:s', strtotime('-7 day')),
            'month' => date('Y-m-d H:i:s', strtotime('-30 day')),
            default => date('Y-m-d 00:00:00')
        };
        
        // 获取前一个时间段的起始日期
        $previousStartDate = match($timeRange) {
            'hour' => date('Y-m-d H:00:00', strtotime('-2 hour')),
            'day' => date('Y-m-d 00:00:00', strtotime('-1 day')),
            'week' => date('Y-m-d H:i:s', strtotime('-14 day')), 
            'month' => date('Y-m-d H:i:s', strtotime('-60 day')),
            default => date('Y-m-d 00:00:00', strtotime('-1 day'))
        };
        
        // 优化版本：简化价格数据查询
        $query->join("(SELECT 
                        object_id,
                        MAX(price) as current_price,
                        MIN(price) as min_price,
                        MAX(price) as max_price,
                        AVG(price) as avg_price
                      FROM ba_sjz_price_history 
                      WHERE timestamp >= '{$startDate}' 
                      AND price > 0
                      GROUP BY object_id) p", 
                     'i.object_id = p.object_id', 'INNER');
        
        // 简化的前一周期价格JOIN
        $query->join("(SELECT object_id, MAX(price) as previous_price 
                      FROM ba_sjz_price_history 
                      WHERE timestamp >= '{$previousStartDate}' 
                      AND timestamp < '{$startDate}' 
                      AND price > 0
                      GROUP BY object_id) prev", 
                     'i.object_id = prev.object_id', 'LEFT');
        
        Log::info('应用优化后的时间范围条件: ' . $timeRange . ', 当前周期: ' . $startDate . ', 前一周期: ' . $previousStartDate);
    }

    /**
     * 应用字段选择 - 优化版本
     *
     * @param \think\db\Query $query
     * @param string $timeRange
     * @return void
     */
    private function applyFieldSelection(\think\db\Query $query, string $timeRange): void
    {
        // 优化字段选择，只选择必要字段
        $fields = [
            'i.object_id',
            'i.object_name as name',
            'i.pic as image_url',
            'i.grade',
            'i.width',
            'i.length',
            'c.primary_class',
            'c.second_class',
            'p.current_price',
            'p.min_price as lowest_price',
            'p.max_price as highest_price', 
            'p.avg_price as average_price',
            'prev.previous_price'
        ];
        
        // 设置查询字段
        $query->field($fields);
        
        Log::info('优化字段选择完成，时间范围: ' . $timeRange . ', 字段数量: ' . count($fields));
    }
    
    /**
     * 检查字段是否存在
     *
     * @param string $fieldName
     * @return bool
     */
    private function fieldExists(string $fieldName): bool
    {
        // 这里简化处理，实际应该根据数据库表结构检查
        // 当前实现中，默认这些字段不存在，它们将在后续数据处理中计算或设置默认值
        $existingFields = [
            'i.object_id', 'i.object_name', 'i.pic', 'i.grade',
            'c.primary_class', 'c.second_class'
        ];
        
        return in_array($fieldName, $existingFields);
    }

    /**
     * 应用排序 - 优化版本
     *
     * @param \think\db\Query $query
     * @param string $sortType
     * @return void
     */
    private function applySorting(\think\db\Query $query, string $sortType): void
    {
        Log::info('应用排序类型: ' . $sortType);
        
        // 简化排序逻辑，移除复杂的SQL计算
        switch ($sortType) {
            case 'highest_price':
                $query->order('p.current_price', 'desc');
                break;
                
            case 'lowest_price':
                $query->order('p.current_price', 'asc');
                break;
                
            case 'increase_percentage':
                // 简化排序，在PHP中处理百分比计算
                $query->where('prev.previous_price', '>', 0)
                      ->orderRaw('(p.current_price - prev.previous_price) / prev.previous_price DESC');
                break;
                
            case 'decrease_percentage':
                $query->where('prev.previous_price', '>', 0)
                      ->orderRaw('(p.current_price - prev.previous_price) / prev.previous_price ASC');
                break;
                
            case 'price_change_absolute':
                $query->where('prev.previous_price', '>', 0)
                      ->orderRaw('ABS(p.current_price - prev.previous_price) DESC');
                break;
                
            case 'price_change_max':
                $query->where('prev.previous_price', '>', 0)
                      ->whereRaw('p.current_price > prev.previous_price')
                      ->orderRaw('(p.current_price - prev.previous_price) DESC');
                break;
                
            case 'price_change_min':
                $query->where('prev.previous_price', '>', 0)
                      ->whereRaw('p.current_price < prev.previous_price')
                      ->orderRaw('(p.current_price - prev.previous_price) ASC');
                break;
                
            default:
                $query->order('p.current_price', 'desc');
                break;
        }
        
        Log::info('应用排序完成: ' . $sortType);
    }

    /**
     * 获取当前排序类型
     *
     * @return string
     */
    private function getCurrentType(): string
    {
        return $this->currentType;
    }

    /**
     * 计算价格对比数据
     *
     * @param array &$item 物品数据引用
     * @return void
     */
    private function calculatePriceComparisons(array &$item): void
    {
        $currentPrice = (float)$item['current_price']; 
        
        // 与平均价格比较
        $averagePrice = isset($item['average_price']) ? (float)$item['average_price'] : $currentPrice;
        if ($averagePrice <= 0) $averagePrice = $currentPrice;
        
        $avgDifference = $currentPrice - $averagePrice;
        $avgPercentage = $averagePrice > 0 ? ($avgDifference / $averagePrice) * 100 : 0;
        
        // 与最高价格比较
        $highestPrice = isset($item['highest_price']) ? (float)$item['highest_price'] : $currentPrice;
        if ($highestPrice <= 0) $highestPrice = $currentPrice;
        
        $highDifference = $currentPrice - $highestPrice;
        $highPercentage = $highestPrice > 0 ? ($highDifference / $highestPrice) * 100 : 0;
        
        // 与最低价格比较
        $lowestPrice = isset($item['lowest_price']) ? (float)$item['lowest_price'] : $currentPrice;
        if ($lowestPrice <= 0) $lowestPrice = $currentPrice;
        
        $lowDifference = $currentPrice - $lowestPrice;
        $lowPercentage = $lowestPrice > 0 ? ($lowDifference / $lowestPrice) * 100 : 0;
        
        // 一次性设置所有比较数据，减少数组操作
        $item['compare_to_average'] = [
            'difference' => round($avgDifference, 2),
            'percentage' => round($avgPercentage, 2)
        ];
        
        $item['compare_to_highest'] = [
            'difference' => round($highDifference, 2),
            'percentage' => round($highPercentage, 2)
        ];
        
        $item['compare_to_lowest'] = [
            'difference' => round($lowDifference, 2),
            'percentage' => round($lowPercentage, 2)
        ];
    }

    /**
     * 获取钥匙卡排行榜数据
     *
     * @param Request $request
     * @return \think\Response
     */
    public function getKeycardRanking(Request $request)
    {
        // 为每个方法创建独立的性能监控器
        $perfMonitor = new PerformanceMonitor();
        $perfMonitor->startTimer('keycard_ranking_request');
        
        try {
            // 参数解析
            $perfMonitor->startTimer('param_parsing');
            $page      = max(1, (int)$request->param('page', 1));
            $pageSize  = max(1, (int)$request->param('page_size', 20));
            $sortParam = $request->param('sort', 'current_price_desc');
            $params = compact('page', 'pageSize', 'sortParam');
            $perfMonitor->endTimer('param_parsing');

            // 缓存处理
            $perfMonitor->startTimer('cache_processing');
            $cacheKey = sprintf('keycard_ranking:%d:%d:%s', $page, $pageSize, $sortParam);
            
            $cacheStart = microtime(true);
            $responseData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($page, $pageSize, $sortParam, $perfMonitor) {
                $perfMonitor->startTimer('ranking_service_call');
                $data = $this->rankingService->getKeycardRankingData($page, $pageSize, $sortParam);
                $perfMonitor->endTimer('ranking_service_call');
                return $data;
            });
            $cacheDuration = microtime(true) - $cacheStart;
            
            $cacheHit = $cacheDuration < 0.01;
            $perfMonitor->recordCacheOperation('get', $cacheKey, $cacheHit, $cacheDuration);
            $perfMonitor->endTimer('cache_processing');
            
            // 记录结果指标
            if (is_array($responseData) && isset($responseData['list'])) {
                $perfMonitor->recordMetric('result_count', count($responseData['list']), 'records');
            }
            
            $perfMonitor->endTimer('keycard_ranking_request');
            $perfMonitor->generateReport('getKeycardRanking', $params);
            
            if ($responseData !== null) {
                return $this->success('获取钥匙卡排行榜成功', $responseData);
            }

            return $this->error('获取钥匙卡排行榜失败');
        } catch (\Throwable $e) {
            $perfMonitor->endTimer('keycard_ranking_request');
            $perfMonitor->generateReport('getKeycardRanking_error', ['error' => $e->getMessage()]);
            
            Log::error('获取钥匙卡排行榜失败: ' . $e->getMessage());
            return $this->error('获取钥匙卡排行榜失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取子弹排行榜数据
     *
     * @param Request $request
     * @return \think\Response
     */
    public function getBulletRanking(Request $request)
    {
        try {
            // -------- 参数解析 --------
            $page      = max(1, (int)$request->param('page', 1));
            $pageSize  = max(1, (int)$request->param('page_size', 20));
            $sortParam = $request->param('sort', 'current_price_desc'); // 目前仅支持 current_price_desc

            // -------- 缓存处理 --------
            $cacheKey = sprintf('bullet_ranking:%d:%d:%s', $page, $pageSize, $sortParam);
            
            // 使用CacheManager获取缓存数据
            $responseData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($page, $pageSize, $sortParam) {
                return $this->rankingService->getBulletRankingData($page, $pageSize, $sortParam);
            });
            
            if ($responseData !== null) {
                return json([
                    'code' => 1,
                    'msg'  => '获取子弹排行榜成功',
                    'time' => time(),
                    'data' => $responseData
                ]);
            }

            // 如果没有缓存数据，返回空结果（应该不会到这里，因为CacheManager会执行回调）
            return json([
                'code' => 0,
                'msg'  => '获取子弹排行榜失败',
                'time' => time(),
                'data' => null
            ]);
        } catch (\Throwable $e) {
            Log::error('获取子弹排行榜失败: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg'  => '获取子弹排行榜失败: ' . $e->getMessage(),
                'time' => time(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取子弹卡包排行榜数据 - 后端配置方案
     *
     * @param Request $request
     * @return \think\Response
     */
    public function getBulletPackageRanking(Request $request)
    {
        try {
            // -------- 参数解析 --------
            $grade = (int)$request->param('grade', 0); // 等级筛选，0表示获取所有等级
            $page = max(1, (int)$request->param('page', 1));
            $pageSize = max(1, (int)$request->param('page_size', 10));

            // -------- 缓存处理 --------
            $cacheKey = sprintf('bullet_package_ranking_v3:%d:%d:%d', $grade, $page, $pageSize);
            
            // 使用CacheManager获取缓存数据
            $responseData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($grade, $page, $pageSize) {
                return $this->rankingService->getBulletPackageRankingData($grade, $page, $pageSize);
            });
            
            return json([
                'code' => 1,
                'msg'  => '获取子弹卡包排行榜成功',
                'time' => time(),
                'data' => $responseData
            ]);
        } catch (\Throwable $e) {
            Log::error('获取子弹卡包排行榜失败: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg'  => '获取子弹卡包排行榜失败: ' . $e->getMessage(),
                'time' => time(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取卡包配置
     */
    private function getBulletPackageConfigs()
    {
        return [
            'grade_3' => [
                'name' => '3级弹药卡包',
                'bullets' => [
                    '5.56x45mm M855' => 200,
                    '9x39mm SP5' => 150,
                    '7.62x54R T46M' => 150,
                    '.45 ACP FMJ' => 180,
                    '5.7x28mm L191' => 200,
                    '4.6x30mm Subsonic SX' => 200,
                    '9x19mm AP6.3' => 200,
                    '.50 AE JHP' => 150,
                    '5.8x42mm DVP88' => 180,
                    '7.62x39mm PS' => 150,
                    '7.62x51mm BPZ' => 150,
                    '5.45x39mm PS' => 200,
                    '.357 Magnum JHP' => 150,
                    '12.7x55mm PS12A' => 80
                ]
            ],
            'grade_4' => [
                'name' => '4级弹药卡包',
                'bullets' => [
                    '9x39mm SP6' => 150,
                    '7.62x54R LPS' => 150,
                    '6.8x51mm FMJ' => 150,
                    '7.62x39mm BP' => 150,
                    '5.8x42mm DBP10' => 150,
                    '.45 ACP AP' => 150,
                    '5.56x45mm M855A1' => 150,
                    '7.62x51mm M80' => 150,
                    '4.6x30mm FMJ SX' => 150,
                    '5.7x28mm SS193' => 150,
                    '.357 Magnum FMJ' => 120,
                    '9x19mm PBP' => 150,
                    '.50 AE FMJ' => 120,
                    '5.45x39mm BT' => 150,
                    '12.7x55mm PD12双头弹' => 60,
                    '12.7x55mm PS12' => 60
                ]
            ],
            'grade_5' => [
                'name' => '5级弹药卡包',
                'bullets' => [
                    '5.8x42mm DVC12' => 240,
                    '5.56x45mm M995' => 240,
                    '4.6x30mm AP SX' => 240,
                    '7.62x39mm AP' => 200,
                    '6.8x51mm Hybrid' => 200,
                    '9x39mm BP' => 200,
                    '5.7x28mm SS190' => 240,
                    '5.45x39mm BS' => 240,
                    '7.62x51mm M62' => 150,
                    '7.62x54R BT' => 120
                ]
            ],
            'pass_advanced' => [
                'name' => '通行证高级子弹自选包',
                'bullets' => [
                    '5.8x42mm DBP10' => 50,
                    '9x39mm SP6' => 40,
                    '6.8x51mm FMJ' => 40,
                    '.45 ACP AP' => 45,
                    '5.56x45mm M855A1' => 45,
                    '7.62x54R LPS' => 35,
                    '7.62x39mm BP' => 40,
                    '5.7x28mm SS193' => 50,
                    '9x19mm PBP' => 50,
                    '4.6x30mm FMJ SX' => 45,
                    '7.62x51mm M80' => 40,
                    '12.7x55mm PD12双头弹' => 25,
                    '12.7x55mm PS12' => 30,
                    '5.45x39mm BT' => 45,
                    '12 Gauge独头 AP-20' => 35
                ]
            ],
            'pass_basic' => [
                'name' => '通行证基础子弹自选包',
                'bullets' => [
                    '9x39mm SP5' => 90,
                    '5.56x45mm M855' => 110,
                    '.45 ACP FMJ' => 100,
                    '5.7x28mm L191' => 110,
                    '7.62x54R T46M' => 80,
                    '5.8x42mm DVP88' => 110,
                    '7.62x39mm PS' => 90,
                    '4.6x30mm Subsonic SX' => 100,
                    '9x19mm AP6.3' => 110,
                    '7.62x51mm BPZ' => 90,
                    '5.45x39mm PS' => 100,
                    '12 Gauge 箭形弹' => 70,
                    '12.7x55mm PS12A' => 65
                ]
            ]
        ];
    }

    /**
     * 获取卡包数据
     */
    private function getBulletPackageData($packageId, $page = 1, $pageSize = 10)
    {
        $configs = $this->getBulletPackageConfigs();

        if (!isset($configs[$packageId])) {
            return ['list' => [], 'total' => 0];
        }

        $config = $configs[$packageId];
        $bulletNames = array_keys($config['bullets']);

        // 获取子弹数据
        $items = Db::name('sjz_items')
            ->alias('i')
            ->join('sjz_item_categories c', 'i.category_id = c.id')
            ->field([
                'i.object_id',
                'i.object_name as name',
                'i.pic as image_url',
                'i.grade',
                'c.second_class_cn as bullet_type'
            ])
            ->where('c.primary_class', 'ammo')
            ->whereIn('i.object_name', $bulletNames)
            ->whereNull('i.delete_time')
            ->select()
            ->toArray();

        // 添加价格信息和卡包数量
        $items = $this->addBulletPriceInfo($items);

        // 为每个子弹添加卡包数量和总价值
        foreach ($items as &$item) {
            $bulletName = $item['name'];
            if (isset($config['bullets'][$bulletName])) {
                $item['quantity'] = (int)$config['bullets'][$bulletName];
                $item['total_value'] = (float)($item['current_price'] * $item['quantity']);
            } else {
                $item['quantity'] = 0;
                $item['total_value'] = 0.0;
            }
        }

        // 按总价值排序
        usort($items, function($a, $b) {
            return $b['total_value'] <=> $a['total_value'];
        });

        // 如果需要分页
        if ($page > 0 && $pageSize > 0) {
            $total = count($items);
            $offset = ($page - 1) * $pageSize;
            $pagedItems = array_slice($items, $offset, $pageSize);

            return [
                'list' => $pagedItems,
                'total' => $total,
                'page' => $page,
                'page_size' => $pageSize
            ];
        }

        return [
            'list' => $items,
            'total' => count($items)
        ];
    }

    /**
     * 为子弹数据添加价格信息
     */
    private function addBulletPriceInfo($items)
    {
        if (empty($items)) {
            return $items;
        }

        $objectIds = array_column($items, 'object_id');

        // 获取当前价格
        $currentPrices = Db::name('sjz_latest_prices')
            ->whereIn('object_id', $objectIds)
            ->column('current_price', 'object_id');

        // 为每个子弹添加价格信息
        foreach ($items as &$item) {
            $objectId = $item['object_id'];
            $currentPrice = $currentPrices[$objectId] ?? 0;

            $item['current_price'] = (float)$currentPrice;
        }

        return $items;
    }

    /**
     * 获取子弹价格数据 - 通用接口，供前端配置驱动使用
     *
     * @param Request $request
     * @return \think\Response
     */
    public function getBulletPrices(Request $request)
    {
        try {
            // -------- 参数解析 --------
            $objectIds = $request->param('object_ids', ''); // 子弹ID列表，逗号分隔
            $grades = $request->param('grades', ''); // 等级列表，逗号分隔，可选

            // -------- 缓存处理 --------
            $cacheKey = 'bullet_prices:' . md5($objectIds . $grades);
            
            // 使用CacheManager获取缓存数据
            $responseData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($objectIds, $grades) {
                return $this->rankingService->getBulletPricesData($objectIds, $grades);
            });
            
            return json([
                'code' => 1,
                'msg'  => '获取子弹价格成功',
                'time' => time(),
                'data' => $responseData
            ]);
        } catch (\Throwable $e) {
            Log::error('获取子弹价格失败: ' . $e->getMessage());
            return json([
                'code' => 0,
                'msg'  => '获取子弹价格失败: ' . $e->getMessage(),
                'time' => time(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取指定物品的最新价格
     * @param int $objectId
     * @return float
     */
    private function getLatestPrice(int $objectId): float
    {
        $price = Db::name('sjz_price_history')
            ->where('object_id', $objectId)
            ->where('price', '>', 0)
            ->order('timestamp', 'desc')
            ->value('price');

        return $price !== null ? (float)$price : 0.0;
    }

    /**
     * 获取指定物品在若干天前最近一次记录的价格
     * @param int $objectId
     * @param int $days
     * @return float
     */
    private function getPriceBeforeDays(int $objectId, int $days): float
    {
        $cutoff = date('Y-m-d H:i:s', strtotime("-{$days} day"));

        $price = Db::name('sjz_price_history')
            ->where('object_id', $objectId)
            ->where('price', '>', 0)
            ->where('timestamp', '<=', $cutoff)
            ->order('timestamp', 'desc')
            ->value('price');

        return $price !== null ? (float)$price : 0.0;
    }

}