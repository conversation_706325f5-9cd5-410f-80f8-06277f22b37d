<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\controller\Frontend;
use app\api\service\PerformanceMonitor;
use app\api\middleware\PerformanceMiddleware;
use think\facade\Log;
use think\facade\Config;

/**
 * API基础控制器
 * 为所有API控制器提供通用功能和性能监控支持
 */
abstract class BaseApiController extends Frontend
{
    /**
     * 获取当前请求的性能监控器
     */
    protected function getPerformanceMonitor(): ?PerformanceMonitor
    {
        return PerformanceMiddleware::getCurrentMonitor();
    }
    
    /**
     * 记录自定义性能指标
     */
    protected function recordMetric(string $name, $value, string $unit = ''): void
    {
        $monitor = $this->getPerformanceMonitor();
        if ($monitor) {
            $monitor->recordMetric($name, $value, $unit);
        }
    }
    
    /**
     * 开始计时器
     */
    protected function startTimer(string $name): void
    {
        $monitor = $this->getPerformanceMonitor();
        if ($monitor) {
            $monitor->startTimer($name);
        }
    }
    
    /**
     * 结束计时器
     */
    protected function endTimer(string $name): float
    {
        $monitor = $this->getPerformanceMonitor();
        if ($monitor) {
            return $monitor->endTimer($name);
        }
        return 0.0;
    }
    
    /**
     * 记录数据库查询性能
     */
    protected function recordDbQuery(string $queryType, float $duration, int $recordCount = 0, string $sql = ''): void
    {
        $monitor = $this->getPerformanceMonitor();
        if ($monitor) {
            $monitor->recordDbQuery($queryType, $duration, $recordCount, $sql);
        }
    }
    
    /**
     * 记录缓存操作
     */
    protected function recordCacheOperation(string $operation, string $key, bool $hit = false, float $duration = 0): void
    {
        $monitor = $this->getPerformanceMonitor();
        if ($monitor) {
            $monitor->recordCacheOperation($operation, $key, $hit, $duration);
        }
    }
    
    /**
     * 检查慢查询
     */
    protected function checkSlowQuery(string $queryType, float $duration, float $threshold = 1.0): void
    {
        $monitor = $this->getPerformanceMonitor();
        if ($monitor) {
            $monitor->checkSlowQuery($queryType, $duration, $threshold);
        }
    }
    
    /**
     * 检查内存使用
     */
    protected function checkMemoryUsage(string $operation, int $threshold = 50 * 1024 * 1024): void
    {
        $monitor = $this->getPerformanceMonitor();
        if ($monitor) {
            $monitor->checkMemoryUsage($operation, $threshold);
        }
    }
    
    /**
     * 统一的成功响应格式
     */
    protected function success(string $msg = '操作成功', $data = null, int $code = 1): \think\Response
    {
        // 记录响应指标
        $this->recordMetric('response_type', 'success');
        
        return json([
            'code' => $code,
            'msg' => $msg,
            'time' => time(),
            'data' => $data
        ]);
    }
    
    /**
     * 统一的错误响应格式
     */
    protected function error(string $msg = '操作失败', $data = null, int $code = 0): \think\Response
    {
        // 记录响应指标
        $this->recordMetric('response_type', 'error');
        $this->recordMetric('error_message', $msg);
        
        return json([
            'code' => $code,
            'msg' => $msg,
            'time' => time(),
            'data' => $data
        ]);
    }
    
    /**
     * 统一的分页响应格式
     */
    protected function paginate(array $list, int $total, int $page, int $pageSize, string $msg = '获取成功'): \think\Response
    {
        // 记录分页指标
        $this->recordMetric('pagination_total', $total, 'records');
        $this->recordMetric('pagination_page', $page);
        $this->recordMetric('pagination_size', $pageSize);
        $this->recordMetric('pagination_returned', count($list), 'records');
        
        return $this->success($msg, [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
            'pages' => ceil($total / $pageSize)
        ]);
    }
    
    /**
     * 执行数据库查询并监控性能
     */
    protected function executeQuery(callable $queryCallback, string $queryType = 'database_query'): mixed
    {
        $this->startTimer($queryType);
        $startTime = microtime(true);
        
        try {
            $result = $queryCallback();
            $duration = microtime(true) - $startTime;
            $this->endTimer($queryType);
            
            // 计算记录数
            $recordCount = 0;
            if (is_array($result)) {
                $recordCount = count($result);
            } elseif (is_numeric($result)) {
                $recordCount = (int)$result;
            }
            
            // 记录查询性能
            $this->recordDbQuery($queryType, $duration, $recordCount);
            
            // 检查慢查询
            $threshold = Config::get('performance.slow_query_thresholds.' . $queryType, 1.0);
            $this->checkSlowQuery($queryType, $duration, $threshold);
            
            return $result;
            
        } catch (\Throwable $e) {
            $duration = microtime(true) - $startTime;
            $this->endTimer($queryType);
            
            // 记录失败的查询
            $this->recordDbQuery($queryType . '_failed', $duration, 0);
            $this->recordMetric('query_error', $e->getMessage());
            
            throw $e;
        }
    }
    
    /**
     * 执行缓存操作并监控性能
     */
    protected function executeCache(callable $cacheCallback, string $cacheKey, string $operation = 'cache_get'): mixed
    {
        $startTime = microtime(true);
        
        try {
            $result = $cacheCallback();
            $duration = microtime(true) - $startTime;
            
            // 判断是否命中缓存（简单判断：耗时很短可能是命中）
            $hit = $duration < 0.01;
            $this->recordCacheOperation($operation, $cacheKey, $hit, $duration);
            
            return $result;
            
        } catch (\Throwable $e) {
            $duration = microtime(true) - $startTime;
            $this->recordCacheOperation($operation . '_failed', $cacheKey, false, $duration);
            
            throw $e;
        }
    }
    
    /**
     * 验证请求参数并记录
     */
    protected function validateParams(array $rules, array $params = null): array
    {
        $this->startTimer('param_validation');
        
        if ($params === null) {
            $params = $this->request->param();
        }
        
        // 记录参数数量
        $this->recordMetric('input_params_count', count($params), 'count');
        
        // 这里可以添加具体的验证逻辑
        // 简化实现，实际项目中应该使用验证器
        
        $this->endTimer('param_validation');
        
        return $params;
    }
    
    /**
     * 记录业务操作日志
     */
    protected function logOperation(string $operation, array $params = [], string $result = 'success'): void
    {
        Log::info('[业务操作] ' . $operation, [
            'controller' => static::class,
            'action' => $this->request->action(),
            'params' => $this->filterSensitiveData($params),
            'result' => $result,
            'ip' => $this->request->ip(),
            'user_agent' => $this->request->header('User-Agent', ''),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        // 记录操作指标
        $this->recordMetric('business_operation', $operation);
        $this->recordMetric('operation_result', $result);
    }
    
    /**
     * 过滤敏感数据
     */
    private function filterSensitiveData(array $data): array
    {
        $sensitiveKeys = Config::get('performance.sensitive_params', ['password', 'token', 'secret']);
        
        foreach ($data as $key => $value) {
            $lowerKey = strtolower($key);
            foreach ($sensitiveKeys as $sensitiveKey) {
                if (strpos($lowerKey, $sensitiveKey) !== false) {
                    $data[$key] = '***';
                    break;
                }
            }
        }
        
        return $data;
    }
    
    /**
     * 获取性能监控配置
     */
    protected function getPerformanceConfig(string $key, $default = null)
    {
        return Config::get('performance.' . $key, $default);
    }
    
    /**
     * 检查是否启用性能监控
     */
    protected function isPerformanceEnabled(): bool
    {
        return Config::get('performance.enabled', true);
    }
}