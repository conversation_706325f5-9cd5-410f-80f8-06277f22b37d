<?php
declare(strict_types=1);

namespace app\api\service;

use think\facade\Cache;
use think\facade\Log;

class CacheManager
{
    // 缓存类型常量
    const TYPE_PRICE_DATA = 'price';     // 价格相关数据 - 整点2分钟更新
    const TYPE_STATIC_DATA = 'static';   // 静态数据 - 长期缓存
    const TYPE_DYNAMIC_DATA = 'dynamic'; // 动态数据 - 短期缓存
    const TYPE_CONFIG_DATA = 'config';   // 配置数据 - 超长缓存

    /**
     * 检查缓存是否启用
     */
    private function isCacheEnabled(string $type = ''): bool
    {
        return BackendConfig::isCacheEnabled($type);
    }

    /**
     * 记录缓存操作日志
     */
    private function logCacheOperation(string $operation, string $key, int $ttl, string $type): void
    {
        // 检查缓存日志是否启用
        if (!BackendConfig::cache('logging.enable', true)) {
            return;
        }

        $level = BackendConfig::cache('logging.level', 'info');
        $message = "Cache {$operation}: {$key} (TTL: {$ttl}s, Type: {$type})";

        switch ($level) {
            case 'debug':
                Log::debug($message);
                break;
            case 'warning':
                Log::warning($message);
                break;
            case 'error':
                Log::error($message);
                break;
            default:
                Log::info($message);
                break;
        }
    }

    /**
     * 获取到整点第2分钟的秒数（价格数据专用）
     */
    private function getSecondsToNextHourPlus2Minutes(): int
    {
        $now = time();
        $currentMinute = date('i', $now);
        
        // 如果当前时间在整点2分钟之前，则缓存到当前整点的2分钟
        if ($currentMinute < 2) {
            $targetTime = strtotime(date('Y-m-d H:02:00', $now));
        } else {
            // 否则缓存到下一个整点的2分钟
            $targetTime = strtotime(date('Y-m-d H:02:00', $now + 3600));
        }
        
        return max(30, $targetTime - $now); // 至少缓存30秒，避免频繁更新
    }
    
    /**
     * 智能缓存策略
     * @param string $type 缓存类型
     * @param array $params 缓存参数（预留用于未来扩展）
     */
    public function getCacheTTL(string $type, array $params = []): int
    {
        // $params 参数预留用于未来扩展，当前版本暂未使用

        // 先检查配置文件中是否有自定义TTL
        $configTTL = BackendConfig::cache("types.{$type}.ttl");
        if ($configTTL !== null && $configTTL !== 'auto') {
            return (int)$configTTL;
        }

        switch ($type) {
            case self::TYPE_PRICE_DATA:
                // 价格相关数据：缓存到整点第2分钟
                return $this->getSecondsToNextHourPlus2Minutes();

            case self::TYPE_STATIC_DATA:
                // 静态数据：长期缓存（24小时）
                return BackendConfig::cache("types.static.ttl", 86400);

            case self::TYPE_CONFIG_DATA:
                // 配置数据：超长缓存（7天）
                return BackendConfig::cache("types.config.ttl", 604800);

            case self::TYPE_DYNAMIC_DATA:
                // 动态数据：短期缓存（5分钟）
                return BackendConfig::cache("types.dynamic.ttl", 300);

            default:
                return 3600; // 默认1小时
        }
    }
    
    /**
     * 统一缓存操作
     */
    public function remember(string $key, string $type, callable $callback, array $params = []): mixed
    {
        // 检查缓存是否启用
        if (!$this->isCacheEnabled($type)) {
            // 缓存未启用，直接执行回调函数
            return $callback();
        }

        $fullKey = $this->buildCacheKey($key, $type, $params);
        $ttl = $this->getCacheTTL($type, $params);

        $data = Cache::store('redis')->get($fullKey);
        if ($data === null) {
            $data = $callback();
            if ($data !== null) {
                Cache::store('redis')->set($fullKey, $data, $ttl);

                // 记录缓存日志
                $this->logCacheOperation('SET', $fullKey, $ttl, $type);
            }
        } else {
            $this->logCacheOperation('HIT', $fullKey, $ttl, $type);
        }

        return $data;
    }
    
    /**
     * 构建缓存键
     */
    private function buildCacheKey(string $key, string $type, array $params): string
    {
        $paramHash = !empty($params) ? ':' . md5(json_encode($params)) : '';
        return "app:{$type}:{$key}{$paramHash}";
    }
    

    
    /**
     * 删除指定类型的缓存
     */
    public function delete(string $key, string $type, array $params = []): bool
    {
        // 检查缓存是否启用
        if (!$this->isCacheEnabled($type)) {
            return true; // 缓存未启用，返回true表示删除成功
        }

        $fullKey = $this->buildCacheKey($key, $type, $params);
        $result = Cache::store('redis')->delete($fullKey);

        if ($result) {
            $this->logCacheOperation('DELETE', $fullKey, 0, $type);
        }

        return $result;
    }
    
    /**
     * 清除指定类型的所有缓存
     */
    public function clearByType(string $type): bool
    {
        $pattern = "app:{$type}:*";
        $keys = Cache::store('redis')->handler()->keys($pattern);
        
        if (!empty($keys)) {
            $result = Cache::store('redis')->handler()->del($keys);
            Log::info("Cache CLEAR: Type {$type}, cleared " . count($keys) . " keys");
            return $result > 0;
        }
        
        return true;
    }
    
    /**
     * 获取缓存状态信息
     */
    public function getStats(): array
    {
        $redis = Cache::store('redis')->handler();
        
        return [
            'memory_usage' => $redis->info('memory')['used_memory_human'] ?? 'unknown',
            'total_keys' => $redis->dbSize(),
            'price_keys' => count($redis->keys('app:price:*')),
            'static_keys' => count($redis->keys('app:static:*')),
            'dynamic_keys' => count($redis->keys('app:dynamic:*')),
            'config_keys' => count($redis->keys('app:config:*')),
        ];
    }
}