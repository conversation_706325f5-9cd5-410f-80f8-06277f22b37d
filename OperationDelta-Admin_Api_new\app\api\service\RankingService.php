<?php
declare(strict_types=1);

namespace app\api\service;

use think\facade\Db;
use think\facade\Log;

/**
 * 排行榜数据服务类
 * 负责处理各种排行榜的数据获取逻辑
 */
class RankingService
{
    /**
     * 获取钥匙卡排行榜数据
     */
    public function getKeycardRankingData(int $page, int $pageSize, string $sortParam): array
    {
        // -------- 动态获取钥匙卡分类ID --------
        $keyCategoryIds = Db::name('sjz_item_categories')
            ->where('second_class', 'key')
            ->column('id');

        if (empty($keyCategoryIds)) {
            // 若未找到分类，直接返回空列表
            return [
                'list'      => [],
                'total'     => 0,
                'page'      => $page,
                'page_size' => $pageSize
            ];
        }

        // -------- 获取全部钥匙卡基础数据 --------
        $items = Db::name('sjz_items')
            ->alias('i')
            ->field(['i.object_id', 'i.object_name as name', 'i.pic as image_url', 'i.grade'])
            ->whereIn('i.category_id', $keyCategoryIds)
            ->whereNull('i.delete_time')
            ->select()
            ->toArray();

        $total = count($items);

        // -------- 批量补充价格信息 --------
        $objectIds = array_column($items, 'object_id');

        // 批量获取所有价格数据
        $currentPrices = $this->getBatchLatestPrices($objectIds);
        $dayPrices = $this->getBatchPricesBeforeDays($objectIds, 1);
        $weekPrices = $this->getBatchPricesBeforeDays($objectIds, 7);
        $monthPrices = $this->getBatchPricesBeforeDays($objectIds, 30);

        foreach ($items as &$item) {
            $objectId = (int)$item['object_id'];

            // 获取价格数据
            $currentPrice = $currentPrices[$objectId] ?? 0.0;
            $dayPrice = $dayPrices[$objectId] ?? 0.0;
            $weekPrice = $weekPrices[$objectId] ?? 0.0;
            $monthPrice = $monthPrices[$objectId] ?? 0.0;

            // 计算涨幅
            $item['current_price'] = $currentPrice;

            // 日
            $item['day_price']       = $dayPrice;
            $item['day_change']      = round($currentPrice - $dayPrice, 2);
            $item['day_change_pct']  = $dayPrice > 0 ? round(($item['day_change'] / $dayPrice) * 100, 2) : 0;

            // 周
            $item['week_price']      = $weekPrice;
            $item['week_change']     = round($currentPrice - $weekPrice, 2);
            $item['week_change_pct'] = $weekPrice > 0 ? round(($item['week_change'] / $weekPrice) * 100, 2) : 0;

            // 月
            $item['month_price']      = $monthPrice;
            $item['month_change']     = round($currentPrice - $monthPrice, 2);
            $item['month_change_pct'] = $monthPrice > 0 ? round(($item['month_change'] / $monthPrice) * 100, 2) : 0;
        }
        unset($item); // 释放引用

        // -------- 排序 & 分页 --------
        usort($items, function ($a, $b) {
            return $b['current_price'] <=> $a['current_price'];
        });

        $list = array_slice($items, ($page - 1) * $pageSize, $pageSize);

        return [
            'list'      => $list,
            'total'     => $total,
            'page'      => $page,
            'page_size' => $pageSize
        ];
    }

    /**
     * 获取子弹排行榜数据
     */
    public function getBulletRankingData(int $page, int $pageSize, string $sortParam): array
    {
        // -------- 动态获取子弹分类ID --------
        $bulletCategoryIds = Db::name('sjz_item_categories')
            ->where('primary_class', 'ammo')
            ->column('id');

        if (empty($bulletCategoryIds)) {
            // 若未找到分类，直接返回空列表
            return [
                'list'      => [],
                'total'     => 0,
                'page'      => $page,
                'page_size' => $pageSize
            ];
        }

        // -------- 获取全部子弹基础数据 --------
        $items = Db::name('sjz_items')
            ->alias('i')
            ->join('sjz_item_categories c', 'i.category_id = c.id')
            ->field([
                'i.object_id',
                'i.object_name as name',
                'i.pic as image_url',
                'i.grade',
                'c.second_class_cn as bullet_type'
            ])
            ->whereIn('i.category_id', $bulletCategoryIds)
            ->whereNull('i.delete_time')
            ->select()
            ->toArray();

        $total = count($items);

        if ($total === 0) {
            return [
                'list'      => [],
                'total'     => 0,
                'page'      => $page,
                'page_size' => $pageSize
            ];
        }

        // -------- 批量获取价格数据 --------
        $objectIds = array_column($items, 'object_id');
        $priceData = [];

        // 获取当前价格
        $currentPrices = Db::name('sjz_latest_prices')
            ->whereIn('object_id', $objectIds)
            ->column('current_price', 'object_id');

        // 批量获取历史价格数据
        $dayPrices = $this->getBatchPricesBeforeDays($objectIds, 1);
        $weekPrices = $this->getBatchPricesBeforeDays($objectIds, 7);
        $monthPrices = $this->getBatchPricesBeforeDays($objectIds, 30);

        foreach ($objectIds as $objectId) {
            $currentPrice = $currentPrices[$objectId] ?? 0;
            $dayPrice = $dayPrices[$objectId] ?? 0;
            $weekPrice = $weekPrices[$objectId] ?? 0;
            $monthPrice = $monthPrices[$objectId] ?? 0;

            $priceData[$objectId] = [
                'current_price' => $currentPrice,
                'day_price' => $dayPrice,
                'day_change' => $currentPrice - $dayPrice,
                'day_change_pct' => $dayPrice > 0 ? (($currentPrice - $dayPrice) / $dayPrice) * 100 : 0,
                'week_price' => $weekPrice,
                'week_change' => $currentPrice - $weekPrice,
                'week_change_pct' => $weekPrice > 0 ? (($currentPrice - $weekPrice) / $weekPrice) * 100 : 0,
                'month_price' => $monthPrice,
                'month_change' => $currentPrice - $monthPrice,
                'month_change_pct' => $monthPrice > 0 ? (($currentPrice - $monthPrice) / $monthPrice) * 100 : 0,
            ];
        }

        // -------- 合并数据并排序 --------
        foreach ($items as &$item) {
            $objectId = $item['object_id'];
            if (isset($priceData[$objectId])) {
                $item = array_merge($item, $priceData[$objectId]);
            } else {
                // 默认价格数据
                $item = array_merge($item, [
                    'current_price' => 0,
                    'day_price' => 0,
                    'day_change' => 0,
                    'day_change_pct' => 0,
                    'week_price' => 0,
                    'week_change' => 0,
                    'week_change_pct' => 0,
                    'month_price' => 0,
                    'month_change' => 0,
                    'month_change_pct' => 0,
                ]);
            }
        }

        // 按当前价格降序排序
        usort($items, function($a, $b) {
            return $b['current_price'] <=> $a['current_price'];
        });

        // -------- 分页处理 --------
        $offset = ($page - 1) * $pageSize;
        $pagedItems = array_slice($items, $offset, $pageSize);

        return [
            'list'      => $pagedItems,
            'total'     => $total,
            'page'      => $page,
            'page_size' => $pageSize
        ];
    }

    /**
     * 获取子弹卡包排行榜数据
     */
    public function getBulletPackageRankingData(int $grade, int $page, int $pageSize): array
    {
        if ($grade > 0) {
            // -------- 获取指定卡包的数据 --------
            return $this->getBulletPackageData($grade, $page, $pageSize);
        } else {
            // -------- 获取所有卡包的数据（不分页，返回完整数据） --------
            return [
                'grade_3' => $this->getBulletPackageData('grade_3', 0, 0),
                'grade_4' => $this->getBulletPackageData('grade_4', 0, 0),
                'grade_5' => $this->getBulletPackageData('grade_5', 0, 0),
                'pass_basic' => $this->getBulletPackageData('pass_basic', 0, 0),
                'pass_advanced' => $this->getBulletPackageData('pass_advanced', 0, 0)
            ];
        }
    }

    /**
     * 获取子弹价格数据
     */
    public function getBulletPricesData(string $objectIds, string $grades): array
    {
        // -------- 构建查询条件 --------
        $query = Db::name('sjz_items')
            ->alias('i')
            ->join('sjz_item_categories c', 'i.category_id = c.id')
            ->field([
                'i.object_id',
                'i.object_name as name',
                'i.pic as image_url',
                'i.grade',
                'c.second_class_cn as bullet_type'
            ])
            ->where('c.primary_class', 'ammo')
            ->whereNull('i.delete_time');

        // 如果指定了object_ids，则只查询指定的子弹
        if (!empty($objectIds)) {
            $objectIdArray = array_filter(array_map('intval', explode(',', $objectIds)));
            if (!empty($objectIdArray)) {
                $query->whereIn('i.object_id', $objectIdArray);
            }
        }

        // 如果指定了grades，则只查询指定等级的子弹
        if (!empty($grades)) {
            $gradeArray = array_filter(array_map('intval', explode(',', $grades)));
            if (!empty($gradeArray)) {
                $query->whereIn('i.grade', $gradeArray);
            }
        }

        $items = $query->select()->toArray();

        // 添加价格信息
        $items = $this->addBulletPriceInfo($items);

        return [
            'list' => $items,
            'total' => count($items)
        ];
    }

    /**
     * 批量获取多个物品的最新价格
     */
    private function getBatchLatestPrices(array $objectIds): array
    {
        if (empty($objectIds)) {
            return [];
        }

        $prices = [];

        // 首先尝试从最新价格表获取
        try {
            $latestPrices = Db::name('sjz_latest_prices')
                ->whereIn('object_id', $objectIds)
                ->column('current_price', 'object_id');

            foreach ($objectIds as $objectId) {
                $prices[$objectId] = isset($latestPrices[$objectId]) ? (float)$latestPrices[$objectId] : 0.0;
            }

            return $prices;
        } catch (\Exception $e) {
            // 如果最新价格表不存在，使用历史表
            return $this->getBatchLatestPricesFromHistory($objectIds);
        }
    }

    /**
     * 从历史表批量获取最新价格
     */
    private function getBatchLatestPricesFromHistory(array $objectIds): array
    {
        if (empty($objectIds)) {
            return [];
        }

        $prices = [];
        $placeholders = str_repeat('?,', count($objectIds) - 1) . '?';

        $sql = "SELECT ph1.object_id, ph1.price
                FROM ba_sjz_price_history ph1
                INNER JOIN (
                    SELECT object_id, MAX(timestamp) as max_timestamp
                    FROM ba_sjz_price_history
                    WHERE object_id IN ({$placeholders}) AND price > 0
                    GROUP BY object_id
                ) ph2 ON ph1.object_id = ph2.object_id AND ph1.timestamp = ph2.max_timestamp
                WHERE ph1.price > 0";

        $results = Db::query($sql, $objectIds);

        foreach ($objectIds as $objectId) {
            $prices[$objectId] = 0.0;
        }

        foreach ($results as $row) {
            $prices[$row['object_id']] = (float)$row['price'];
        }

        return $prices;
    }

    /**
     * 批量获取指定物品在若干天前的价格
     */
    private function getBatchPricesBeforeDays(array $objectIds, int $days): array
    {
        if (empty($objectIds)) {
            return [];
        }

        $cutoff = date('Y-m-d H:i:s', strtotime("-{$days} day"));
        $prices = [];
        $placeholders = str_repeat('?,', count($objectIds) - 1) . '?';

        $sql = "SELECT ph1.object_id, ph1.price
                FROM ba_sjz_price_history ph1
                INNER JOIN (
                    SELECT object_id, MAX(timestamp) as max_timestamp
                    FROM ba_sjz_price_history
                    WHERE object_id IN ({$placeholders})
                    AND timestamp <= ?
                    AND price > 0
                    GROUP BY object_id
                ) ph2 ON ph1.object_id = ph2.object_id AND ph1.timestamp = ph2.max_timestamp
                WHERE ph1.price > 0";

        $params = array_merge($objectIds, [$cutoff]);
        $results = Db::query($sql, $params);

        foreach ($objectIds as $objectId) {
            $prices[$objectId] = 0.0;
        }

        foreach ($results as $row) {
            $prices[$row['object_id']] = (float)$row['price'];
        }

        return $prices;
    }

    /**
     * 获取指定物品的最新价格（保持向后兼容）
     */
    private function getLatestPrice(int $objectId): float
    {
        $prices = $this->getBatchLatestPrices([$objectId]);
        return $prices[$objectId] ?? 0.0;
    }

    /**
     * 获取指定物品在若干天前最近一次记录的价格（保持向后兼容）
     */
    private function getPriceBeforeDays(int $objectId, int $days): float
    {
        $prices = $this->getBatchPricesBeforeDays([$objectId], $days);
        return $prices[$objectId] ?? 0.0;
    }

    /**
     * 获取卡包配置
     */
    private function getBulletPackageConfigs()
    {
        return [
            'grade_3' => [
                'name' => '3级弹药卡包',
                'bullets' => [
                    '5.56x45mm M855' => 200,
                    '9x39mm SP5' => 150,
                    '7.62x54R T46M' => 150,
                    '.45 ACP FMJ' => 180,
                    '5.7x28mm L191' => 200,
                    '4.6x30mm Subsonic SX' => 200,
                    '9x19mm AP6.3' => 200,
                    '.50 AE JHP' => 150,
                    '5.8x42mm DVP88' => 180,
                    '7.62x39mm PS' => 150,
                    '7.62x51mm BPZ' => 150,
                    '5.45x39mm PS' => 200,
                    '.357 Magnum JHP' => 150,
                    '12.7x55mm PS12A' => 80
                ]
            ],
            'grade_4' => [
                'name' => '4级弹药卡包',
                'bullets' => [
                    '9x39mm SP6' => 150,
                    '7.62x54R LPS' => 150,
                    '6.8x51mm FMJ' => 150,
                    '7.62x39mm BP' => 150,
                    '5.8x42mm DBP10' => 150,
                    '.45 ACP AP' => 150,
                    '5.56x45mm M855A1' => 150,
                    '7.62x51mm M80' => 150,
                    '4.6x30mm FMJ SX' => 150,
                    '5.7x28mm SS193' => 150,
                    '.357 Magnum FMJ' => 120,
                    '9x19mm PBP' => 150,
                    '.50 AE FMJ' => 120,
                    '5.45x39mm BT' => 150,
                    '12.7x55mm PD12双头弹' => 60,
                    '12.7x55mm PS12' => 60
                ]
            ],
            'grade_5' => [
                'name' => '5级弹药卡包',
                'bullets' => [
                    '5.8x42mm DVC12' => 240,
                    '5.56x45mm M995' => 240,
                    '4.6x30mm AP SX' => 240,
                    '7.62x39mm AP' => 200,
                    '6.8x51mm Hybrid' => 200,
                    '9x39mm BP' => 200,
                    '5.7x28mm SS190' => 240,
                    '5.45x39mm BS' => 240,
                    '7.62x51mm M62' => 150,
                    '7.62x54R BT' => 120
                ]
            ],
            'pass_advanced' => [
                'name' => '通行证高级子弹自选包',
                'bullets' => [
                    '5.8x42mm DBP10' => 50,
                    '9x39mm SP6' => 40,
                    '6.8x51mm FMJ' => 40,
                    '.45 ACP AP' => 45,
                    '5.56x45mm M855A1' => 45,
                    '7.62x54R LPS' => 35,
                    '7.62x39mm BP' => 40,
                    '5.7x28mm SS193' => 50,
                    '9x19mm PBP' => 50,
                    '4.6x30mm FMJ SX' => 45,
                    '7.62x51mm M80' => 40,
                    '12.7x55mm PD12双头弹' => 25,
                    '12.7x55mm PS12' => 30,
                    '5.45x39mm BT' => 45,
                    '12 Gauge独头 AP-20' => 35
                ]
            ],
            'pass_basic' => [
                'name' => '通行证基础子弹自选包',
                'bullets' => [
                    '9x39mm SP5' => 90,
                    '5.56x45mm M855' => 110,
                    '.45 ACP FMJ' => 100,
                    '5.7x28mm L191' => 110,
                    '7.62x54R T46M' => 80,
                    '5.8x42mm DVP88' => 110,
                    '7.62x39mm PS' => 90,
                    '4.6x30mm Subsonic SX' => 100,
                    '9x19mm AP6.3' => 110,
                    '7.62x51mm BPZ' => 90,
                    '5.45x39mm PS' => 100,
                    '12 Gauge 箭形弹' => 70,
                    '12.7x55mm PS12A' => 65
                ]
            ]
        ];
    }

    /**
     * 获取卡包数据
     */
    private function getBulletPackageData($packageId, $page = 1, $pageSize = 10)
    {
        $configs = $this->getBulletPackageConfigs();

        if (!isset($configs[$packageId])) {
            return ['list' => [], 'total' => 0];
        }

        $config = $configs[$packageId];
        $bulletNames = array_keys($config['bullets']);

        // 获取子弹数据
        $items = Db::name('sjz_items')
            ->alias('i')
            ->join('sjz_item_categories c', 'i.category_id = c.id')
            ->field([
                'i.object_id',
                'i.object_name as name',
                'i.pic as image_url',
                'i.grade',
                'c.second_class_cn as bullet_type'
            ])
            ->where('c.primary_class', 'ammo')
            ->whereIn('i.object_name', $bulletNames)
            ->whereNull('i.delete_time')
            ->select()
            ->toArray();

        // 添加价格信息和卡包数量
        $items = $this->addBulletPriceInfo($items);

        // 为每个子弹添加卡包数量和总价值
        foreach ($items as &$item) {
            $bulletName = $item['name'];
            if (isset($config['bullets'][$bulletName])) {
                $item['quantity'] = (int)$config['bullets'][$bulletName];
                $item['total_value'] = (float)($item['current_price'] * $item['quantity']);
            } else {
                $item['quantity'] = 0;
                $item['total_value'] = 0.0;
            }
        }

        // 按总价值排序
        usort($items, function($a, $b) {
            return $b['total_value'] <=> $a['total_value'];
        });

        // 如果需要分页
        if ($page > 0 && $pageSize > 0) {
            $total = count($items);
            $offset = ($page - 1) * $pageSize;
            $pagedItems = array_slice($items, $offset, $pageSize);

            return [
                'list' => $pagedItems,
                'total' => $total,
                'page' => $page,
                'page_size' => $pageSize
            ];
        }

        return [
            'list' => $items,
            'total' => count($items)
        ];
    }

    /**
     * 为子弹数据添加价格信息
     */
    private function addBulletPriceInfo($items)
    {
        if (empty($items)) {
            return $items;
        }

        $objectIds = array_column($items, 'object_id');

        // 获取当前价格
        $currentPrices = Db::name('sjz_latest_prices')
            ->whereIn('object_id', $objectIds)
            ->column('current_price', 'object_id');

        // 为每个子弹添加价格信息
        foreach ($items as &$item) {
            $objectId = $item['object_id'];
            $currentPrice = $currentPrices[$objectId] ?? 0;

            $item['current_price'] = (float)$currentPrice;
        }

        return $items;
    }
}